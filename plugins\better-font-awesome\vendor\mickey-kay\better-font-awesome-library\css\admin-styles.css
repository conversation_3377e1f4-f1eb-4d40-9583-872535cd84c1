/**
 * Better Font Awesome admin styles
 *
 * @package   Better Font Awesome Library
 * <AUTHOR> & <PERSON> <<EMAIL>>
 * @license   GPL-2.0+
 * @link      https://github.com/MickeyKay/better-font-awesome-library
 * @copyright 2014 MIGHT<PERSON><PERSON><PERSON><PERSON> & <PERSON> Kay
 */

/**
 * TinyMCE Button & Popup
 */

.bfa-iconpicker {
    display: inline-block;
}

.bfa-iconpicker .iconpicker-popover {
	z-index: 1000;
	background-color: #fff;
	border: 1px solid #ccc;
	border-radius: 4px;
	-webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
	box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.bfa-iconpicker .iconpicker-popover .arrow {
	display: none !important;
}

.bfa-iconpicker .iconpicker-item {
	/* Override some unfortunate WP default admin mobile styles */
	width: 14px !important;
	height: 14px !important;
	padding: 14px !important;
	font-size: 14px !important;
	line-height: 1 !important;
	box-shadow: 0 0 0 1px #ddd !important;
}

.bfa-iconpicker input[type="search"] {
	width: 100%;
}

.bfa-iconpicker .iconpicker-component i {
	display: none;
}
