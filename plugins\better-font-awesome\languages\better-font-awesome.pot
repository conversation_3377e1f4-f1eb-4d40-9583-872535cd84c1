# Copyright (C) 2023 <PERSON>
# This file is distributed under the GPLv2+.
msgid ""
msgstr ""
"Project-Id-Version: Better Font Awesome 2.0.4\n"
"Report-Msgid-Bugs-To: "
"https://wordpress.org/support/plugin/better-font-awesome\n"
"POT-Creation-Date: 2023-01-15 18:06:13+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2023-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"X-Generator: grunt-wp-i18n 1.0.3\n"

#. Plugin Name of the plugin/theme
msgid "Better Font Awesome"
msgstr ""

#: better-font-awesome.php:253
msgid ""
"It appears that Better Font Awesome is missing it's <a "
"href=\"https://github.com/MickeyKay/better-font-awesome-library\" "
"target=\"_blank\">core library</a>, which typically occurs when cloning the "
"Git repository and failing to run <code>composer install</code>. Please "
"refer to the plugin's <a "
"href=\"https://github.com/MickeyKay/better-font-awesome\" "
"target=\"_blank\">installation instructions</a> for details on how to "
"properly install Better Font Awesome via Git. If you installed from within "
"WordPress, or via the wordpress.org repo, then chances are the install "
"failed and you can try again. If the issue persists, please create a new "
"topic on the plugin's <a "
"href=\"http://wordpress.org/support/plugin/better-font-awesome\" "
"target=\"_blank\">support forum</a> or file an issue on the <a "
"href=\"https://github.com/MickeyKay/better-font-awesome/issues\" "
"target=\"_blank\">Github repo</a>."
msgstr ""

#: better-font-awesome.php:254
msgid "Back to the plugins page &rarr;"
msgstr ""

#: better-font-awesome.php:368
msgid "Save Settings"
msgstr ""

#: better-font-awesome.php:397
msgid "Font Awesome version"
msgstr ""

#: better-font-awesome.php:405
msgid "Version check frequency"
msgstr ""

#: better-font-awesome.php:413
msgid "Include v4 CSS shim"
msgstr ""

#: better-font-awesome.php:419
msgid ""
"Include the Font Awesome v4 CSS shim to support legacy icons (<a "
"href=\"https://fontawesome.com/how-to-use/on-the-web/setup/upgrading-from-"
"version-4#name-changes\" target=\"_blank\">more details</a>)."
msgstr ""

#: better-font-awesome.php:425
msgid "Remove existing Font Awesome"
msgstr ""

#: better-font-awesome.php:431
msgid ""
"Attempt to remove Font Awesome CSS and shortcodes added by other plugins "
"and themes."
msgstr ""

#: better-font-awesome.php:437
msgid "Hide admin notices"
msgstr ""

#: better-font-awesome.php:443
msgid ""
"Hide the default admin warnings that are shown when API and CDN errors "
"occur."
msgstr ""

#: better-font-awesome.php:492
msgid ""
"Settings were not saved due to a missing nonce. Refresh the page and try "
"again."
msgstr ""

#: better-font-awesome.php:507
msgid "Settings saved."
msgstr ""

#: better-font-awesome.php:531
#. translators: placeholder is the numeric current version number.
msgid ""
"%s (The plugin automatically uses the latest version of Font Awesome, and "
"checks for updates at this frequency)"
msgstr ""

#: vendor/mickey-kay/better-font-awesome-library/better-font-awesome-library.php:928
msgid "Insert Icon"
msgstr ""

#: vendor/mickey-kay/better-font-awesome-library/better-font-awesome-library.php:955
msgid ""
"It looks like something went wrong when trying to fetch data from the Font "
"Awesome API:"
msgstr ""

#: vendor/mickey-kay/better-font-awesome-library/better-font-awesome-library.php:966
msgid ""
"Don't worry! Better Font Awesome will still render using the included "
"fallback version:</b> "
msgstr ""

#: vendor/mickey-kay/better-font-awesome-library/better-font-awesome-library.php:967
msgid ""
"This may be the result of a temporary server or connectivity issue which "
"will resolve shortly. However if the problem persists please file a support "
"ticket on the %splugin forum%s, citing the errors listed above. "
msgstr ""

#. Plugin URI of the plugin/theme
msgid "http://wordpress.org/plugins/better-font-awesome"
msgstr ""

#. Description of the plugin/theme
msgid "The ultimate Font Awesome icon plugin for WordPress."
msgstr ""

#. Author of the plugin/theme
msgid "Mickey Kay"
msgstr ""

#. Author URI of the plugin/theme
msgid "<EMAIL>"
msgstr ""